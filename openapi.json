{"openapi": "3.1.0", "info": {"title": "游戏管理系统API", "description": "游戏管理系统的RESTful API文档，支持微信小程序登录和JWT认证", "contact": {"name": "linmt", "url": "https://github.com/linmt", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "2.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "security": [{"bearerAuth": []}], "paths": {"/sanBing/group": {"put": {"tags": ["san-bing-controller"], "summary": "更新联盟分组", "description": "联盟管理者更新分组信息", "operationId": "updateGroup", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GroupUpdateDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}}, "security": [{"sessionAuth": []}]}, "post": {"tags": ["san-bing-controller"], "summary": "创建联盟分组", "description": "联盟管理者创建分组", "operationId": "createGroup", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GroupCreateDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}}, "security": [{"sessionAuth": []}]}}, "/sanBing/coalition": {"put": {"tags": ["san-bing-controller"], "summary": "更新联盟信息", "description": "更新联盟名称和代码，只有联盟管理者可以操作", "operationId": "updateCoalition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CoalitionUpdateDTO"}}}, "required": true}, "responses": {"200": {"description": "OK"}}, "security": [{"sessionAuth": []}]}, "post": {"tags": ["san-bing-controller"], "summary": "创建联盟", "description": "创建一个新的三冰联盟", "operationId": "coalition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CoalitionCreateDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CoalitionKey"}}}}}, "security": [{"sessionAuth": []}]}}, "/sanBing/account": {"put": {"tags": ["san-bing-controller"], "summary": "更新账号信息", "description": "更新联盟账号信息，只有账号创建者和联盟管理者可以操作", "operationId": "updateAccount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountUpdateDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}}, "security": [{"sessionAuth": []}]}, "post": {"tags": ["san-bing-controller"], "summary": "创建账号", "description": "用户在指定区域创建账号，每个用户最多创建2个账号", "operationId": "createAccount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCreateDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AccountCreateResult"}}}}}, "security": [{"sessionAuth": []}]}}, "/user/wechat-login": {"post": {"tags": ["user-controller"], "summary": "微信小程序登录", "description": "通过微信小程序code进行登录", "operationId": "we<PERSON>hat<PERSON><PERSON>in", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeChatLoginDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/LoginResult"}}}}}}}, "/user/logout": {"post": {"tags": ["user-controller"], "summary": "用户登出", "description": "清除用户登录状态", "operationId": "logout", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/sanBing/group/add-account": {"post": {"tags": ["san-bing-controller"], "summary": "添加账号到分组", "description": "联盟管理者将账号添加到具体分组。如果是GUAN_DU类型分组，会自动处理从另一个GUAN_DU分组的移动", "operationId": "addAccountToGroup", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountAddToGroupDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}}, "security": [{"sessionAuth": []}]}}, "/sanBing/account/{accountId}/quit": {"post": {"tags": ["san-bing-controller"], "summary": "账号退出联盟", "description": "账号退出联盟，同时退出该联盟下的所有分组", "operationId": "quitCoalition", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}}, "security": [{"sessionAuth": []}]}}, "/sanBing/account/join": {"post": {"tags": ["san-bing-controller"], "summary": "申请加入联盟", "description": "账号申请加入联盟，只能申请加入同区的联盟", "operationId": "joinCoalition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountJoinCoalitionDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}}, "security": [{"sessionAuth": []}]}}, "/sanBing/account/approve": {"post": {"tags": ["san-bing-controller"], "summary": "审核账号申请", "description": "联盟管理者审核账号的加入申请", "operationId": "approveAccount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountApproveDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}}, "security": [{"sessionAuth": []}]}}, "/sanBing/account/apply-group": {"post": {"tags": ["san-bing-controller"], "summary": "申请加入分组类型", "description": "账号申请加入分组类型，每种类型只能申请一次", "operationId": "applyJoinGroup", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountApplyGroupDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}}, "security": [{"sessionAuth": []}]}}, "/user/profile": {"get": {"tags": ["user-controller"], "summary": "获取用户详细信息", "description": "获取当前登录用户的详细信息（与/info接口相同）", "operationId": "getUserProfile", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UserInfo"}}}}}, "security": [{"bearerAuth": []}]}}, "/user/info": {"get": {"tags": ["user-controller"], "summary": "获取当前用户信息", "description": "获取当前登录用户的基本信息", "operationId": "getUserInfo", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UserInfo"}}}}}, "security": [{"bearerAuth": []}]}}, "/sanBing/coalition/{coalitionId}": {"get": {"tags": ["san-bing-controller"], "summary": "查询联盟详细信息", "description": "根据联盟ID查询联盟详细信息，包括分组和账号信息", "operationId": "getCoalitionDetail", "parameters": [{"name": "coalitionId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CoalitionDetailInfo"}}}}}, "security": [{"sessionAuth": []}]}}, "/sanBing/coalition/my": {"get": {"tags": ["san-bing-controller"], "summary": "查询我的联盟", "description": "查询当前用户创建的所有联盟信息", "operationId": "getUserCoalitions", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MyCoalitionInfo"}}}}}}, "security": [{"sessionAuth": []}]}}, "/sanBing/account/my": {"get": {"tags": ["san-bing-controller"], "summary": "查询用户账号信息", "description": "查询当前用户创建的所有账号信息，按区域分组", "operationId": "getUserAccounts", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AreaAccount"}}}}}}, "security": [{"sessionAuth": []}]}}, "/sanBing/group/{groupId}": {"delete": {"tags": ["san-bing-controller"], "summary": "删除联盟分组", "description": "联盟管理者删除分组，清空分组内所有账号", "operationId": "deleteGroup", "parameters": [{"name": "groupId", "in": "path", "description": "分组ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}}, "security": [{"sessionAuth": []}]}}, "/sanBing/group/clear/{coalitionId}/{groupType}": {"delete": {"tags": ["san-bing-controller"], "summary": "清空分组类型账号", "description": "联盟管理者清空某个类型分组的所有账号", "operationId": "clearGroupTypeAccounts", "parameters": [{"name": "coalitionId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "groupType", "in": "path", "description": "分组类型", "required": true, "schema": {"type": "string", "enum": ["GUAN_DU1", "GUAN_DU2", "GONG_CHENG"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}}, "security": [{"sessionAuth": []}]}}, "/sanBing/account/{accountId}": {"delete": {"tags": ["san-bing-controller"], "summary": "移除联盟账号", "description": "联盟管理者移除账号，同时清空该账号的所有分组信息", "operationId": "removeAccount", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}}, "security": [{"sessionAuth": []}]}}}, "components": {"schemas": {"GroupUpdateDTO": {"type": "object", "description": "分组更新信息", "properties": {"groupId": {"type": "integer", "format": "int64", "description": "分组ID", "example": 123456}, "name": {"type": "string", "description": "分组名称", "example": "攻城组1"}, "task": {"type": "string", "description": "分组任务", "example": "负责攻城战斗"}}, "required": ["groupId", "name"]}, "OperationResult": {"type": "object", "description": "操作结果", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": true}, "message": {"type": "string", "description": "结果消息", "example": "操作成功"}}}, "CoalitionUpdateDTO": {"type": "object", "description": "更新联盟信息请求", "properties": {"coalitionId": {"type": "integer", "format": "int64", "description": "联盟ID", "example": 123456}, "name": {"type": "string", "description": "联盟名称", "example": "无敌联盟"}, "code": {"type": "string", "description": "联盟代码", "example": "ABC123"}}, "required": ["coalitionId", "code", "name"]}, "AccountUpdateDTO": {"type": "object", "description": "账号更新信息", "properties": {"accountId": {"type": "integer", "format": "int64", "description": "账号ID", "example": 123456}, "name": {"type": "string", "description": "账号姓名", "example": "张三"}, "level": {"type": "integer", "format": "int32", "description": "联盟阶位", "example": 3, "maximum": 5, "minimum": 1}, "combatPower": {"type": "integer", "format": "int32", "description": "战力", "example": 100000, "minimum": 0}, "addition": {"type": "number", "format": "double", "description": "加成", "example": 1.5, "minimum": 0}, "gatheringCapacity": {"type": "integer", "format": "int32", "description": "集结容量", "example": 200, "minimum": 0}}, "required": ["accountId", "combatPower", "level", "name"]}, "WeChatLoginDTO": {"type": "object", "description": "微信小程序登录信息", "properties": {"code": {"type": "string", "description": "微信小程序登录凭证code", "example": "************"}, "nickName": {"type": "string", "description": "用户昵称", "example": "微信用户"}, "avatarUrl": {"type": "string", "description": "用户头像URL", "example": "https://thirdwx.qlogo.cn/mmopen/..."}, "gender": {"type": "integer", "format": "int32", "description": "用户性别：0-未知，1-男，2-女", "example": 1}, "country": {"type": "string", "description": "用户所在国家", "example": "China"}, "province": {"type": "string", "description": "用户所在省份", "example": "Guangdong"}, "city": {"type": "string", "description": "用户所在城市", "example": "Shenzhen"}, "language": {"type": "string", "description": "用户语言", "example": "zh_CN"}}, "required": ["code"]}, "LoginResult": {"type": "object", "description": "登录结果", "properties": {"userId": {"type": "integer", "format": "int64", "description": "用户ID", "example": 1234567890}, "nickName": {"type": "string", "description": "用户昵称", "example": "微信用户"}, "avatarUrl": {"type": "string", "description": "用户头像URL", "example": "https://thirdwx.qlogo.cn/mmopen/..."}, "isNewUser": {"type": "boolean", "description": "是否为新用户", "example": false}, "token": {"type": "string", "description": "JWT认证令牌", "example": "eyJhbGciOiJIUzUxMiJ9..."}}}, "GroupCreateDTO": {"type": "object", "description": "分组创建信息", "properties": {"coalitionId": {"type": "integer", "format": "int64", "description": "联盟ID", "example": 123456}, "name": {"type": "string", "description": "分组名称", "example": "攻城组1"}, "type": {"type": "string", "description": "分组类型", "enum": ["GUAN_DU1", "GUAN_DU2", "GONG_CHENG"], "example": "GONG_CHENG"}, "task": {"type": "string", "description": "分组任务", "example": "负责攻城战斗"}}, "required": ["coalitionId", "name", "type"]}, "AccountAddToGroupDTO": {"type": "object", "description": "添加账号到分组信息", "properties": {"groupId": {"type": "integer", "format": "int64", "description": "分组ID", "example": 123456}, "accountId": {"type": "integer", "format": "int64", "description": "账号ID", "example": 789012}}, "required": ["accountId", "groupId"]}, "CoalitionCreateDTO": {"type": "object", "description": "联盟创建信息", "properties": {"areaCode": {"type": "integer", "format": "int32", "minimum": 1}, "name": {"type": "string"}}, "required": ["areaCode", "name"]}, "CoalitionKey": {"type": "object", "properties": {"code": {"type": "string"}}}, "AccountCreateDTO": {"type": "object", "description": "账号创建信息", "properties": {"areaCode": {"type": "integer", "format": "int32", "description": "区域代码", "example": 1, "minimum": 1}, "name": {"type": "string", "description": "账号姓名", "example": "张三"}, "combatPower": {"type": "integer", "format": "int32", "description": "战力", "example": 100000, "minimum": 0}, "addition": {"type": "number", "format": "double", "description": "加成", "example": 1.5, "minimum": 0}, "gatheringCapacity": {"type": "integer", "format": "int32", "description": "集结容量", "example": 200, "minimum": 0}}, "required": ["areaCode", "combatPower", "name"]}, "AccountCreateResult": {"type": "object", "description": "创建账号结果", "properties": {"accountId": {"type": "integer", "format": "int64", "description": "账号ID", "example": 123456}, "name": {"type": "string", "description": "账号姓名", "example": "张三"}, "areaCode": {"type": "integer", "format": "int32", "description": "区域代码", "example": 1}, "message": {"type": "string", "description": "结果消息", "example": "账号创建成功"}}}, "AccountJoinCoalitionDTO": {"type": "object", "description": "加入联盟申请信息", "properties": {"accountId": {"type": "integer", "format": "int64", "description": "账号ID", "example": 123456}, "coalitionCode": {"type": "string", "description": "联盟代码", "example": "ABC123"}, "level": {"type": "integer", "format": "int32", "description": "联盟阶位", "example": 3, "maximum": 5, "minimum": 1}}, "required": ["accountId", "coalitionCode", "level"]}, "AccountApproveDTO": {"type": "object", "description": "审核信息", "properties": {"accountId": {"type": "integer", "format": "int64", "description": "账号ID", "example": 123456}, "approved": {"type": "boolean", "description": "是否通过审核", "example": true}}, "required": ["accountId", "approved"]}, "AccountApplyGroupDTO": {"type": "object", "description": "申请加入分组信息", "properties": {"accountId": {"type": "integer", "format": "int64", "description": "账号ID", "example": 123456}, "groupType": {"type": "string", "description": "分组类型", "enum": ["GUAN_DU1", "GUAN_DU2", "GONG_CHENG"], "example": "GONG_CHENG"}}, "required": ["accountId", "groupType"]}, "UserInfo": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "lastLoginTime": {"type": "string", "format": "date-time"}}}, "AccountInfo": {"type": "object", "description": "账号信息", "properties": {"accountId": {"type": "integer", "format": "int64", "description": "账号ID", "example": 123456}, "name": {"type": "string", "description": "账号姓名", "example": "张三"}, "level": {"type": "integer", "format": "int32", "description": "等级", "example": 3}, "combatPower": {"type": "integer", "format": "int32", "description": "战力", "example": 100000}, "addition": {"type": "number", "format": "double", "description": "加成", "example": 1.5}, "gatheringCapacity": {"type": "integer", "format": "int32", "description": "集结容量", "example": 200}, "status": {"type": "string", "description": "审核状态", "enum": ["TO_BE_APPROVED", "APPROVED", "REJECT"], "example": "APPROVED"}, "coalition": {"$ref": "#/components/schemas/CoalitionSimpleInfo", "description": "联盟信息"}}}, "CoalitionDetailInfo": {"type": "object", "description": "联盟详细信息", "properties": {"coalitionId": {"type": "integer", "format": "int64", "description": "联盟ID", "example": 123456}, "name": {"type": "string", "description": "联盟名称", "example": "无敌联盟"}, "code": {"type": "string", "description": "联盟代码", "example": "ABC123"}, "areaCode": {"type": "integer", "format": "int32", "description": "区号", "example": 1}, "managerId": {"type": "integer", "format": "int64", "description": "管理员ID", "example": 789012}, "isManager": {"type": "boolean", "description": "是否为管理员", "example": true}, "groupsByType": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/GroupInfo"}}, "description": "按分组类型分组的成员信息"}, "applyAccountsByType": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/AccountInfo"}}, "description": "按申请分组类型分组的账号信息"}, "pendingAccounts": {"type": "array", "description": "待审核的账号列表（仅管理员可见）", "items": {"$ref": "#/components/schemas/AccountInfo"}}}}, "CoalitionSimpleInfo": {"type": "object", "description": "联盟简要信息", "properties": {"coalitionId": {"type": "integer", "format": "int64", "description": "联盟ID", "example": 123456}, "name": {"type": "string", "description": "联盟名称", "example": "无敌联盟"}, "code": {"type": "string", "description": "联盟代码", "example": "ABC123"}}}, "GroupInfo": {"type": "object", "description": "分组信息", "properties": {"groupId": {"type": "integer", "format": "int64", "description": "分组ID", "example": 123456}, "name": {"type": "string", "description": "分组名称", "example": "攻城组1"}, "type": {"type": "string", "description": "分组类型", "enum": ["GUAN_DU1", "GUAN_DU2", "GONG_CHENG"], "example": "GONG_CHENG"}, "task": {"type": "string", "description": "分组任务", "example": "负责攻城战斗"}, "accounts": {"type": "array", "description": "分组账号列表", "items": {"$ref": "#/components/schemas/AccountInfo"}}}}, "MyCoalitionInfo": {"type": "object", "description": "我的联盟信息", "properties": {"coalitionId": {"type": "integer", "format": "int64", "description": "联盟ID", "example": 123456}, "name": {"type": "string", "description": "联盟名称", "example": "无敌联盟"}, "code": {"type": "string", "description": "联盟代码", "example": "ABC123"}, "areaCode": {"type": "integer", "format": "int32", "description": "区号", "example": 1}, "memberCount": {"type": "integer", "format": "int64", "description": "联盟成员数量", "example": 25}, "pendingMemberCount": {"type": "integer", "format": "int64", "description": "待审核成员数量", "example": 3}}}, "AreaAccount": {"type": "object", "description": "区域账号信息", "properties": {"areaCode": {"type": "integer", "format": "int32", "description": "区域代码", "example": 1}, "accounts": {"type": "array", "description": "该区域中用户创建的所有账号信息", "items": {"$ref": "#/components/schemas/AccountInfo"}}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "description": "基于JWT的认证，请先通过微信小程序登录接口获取token，然后在Authorization头中添加'Bearer {token}'", "scheme": "bearer", "bearerFormat": "JWT"}}}}