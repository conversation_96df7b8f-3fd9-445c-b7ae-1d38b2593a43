import Taro from '@tarojs/taro'

// API基础配置
const BASE_URL = process.env.NODE_ENV === 'development'
  ? 'http://localhost:8080'
  : 'https://your-production-api.com'

// 获取JWT token
const getToken = (): string | null => {
  try {
    return Taro.getStorageSync('jwt_token')
  } catch (error) {
    console.error('获取token失败:', error)
    return null
  }
}

// 保存JWT token
export const setToken = (token: string): void => {
  try {
    Taro.setStorageSync('jwt_token', token)
  } catch (error) {
    console.error('保存token失败:', error)
  }
}

// 清除JWT token
export const clearToken = (): void => {
  try {
    Taro.removeStorageSync('jwt_token')
  } catch (error) {
    console.error('清除token失败:', error)
  }
}

// 请求拦截器
const request = (options: any) => {
  const { url, data, method = 'GET', header = {}, showError = true } = options

  // 获取JWT token并添加到请求头
  const token = getToken()
  const authHeader = token ? { 'Authorization': `Bearer ${token}` } : {}

  return Taro.request({
    url: BASE_URL + url,
    data,
    method,
    header: {
      'Content-Type': 'application/json',
      ...authHeader,
      ...header
    },
  }).then(response => {
    const { statusCode, data: responseData } = response

    if (statusCode >= 200 && statusCode < 300) {
      // 统一处理后端响应格式 {"success": true, "message": "成功", "data": {}}
      if (responseData && typeof responseData === 'object' && 'success' in responseData) {
        if (responseData.success) {
          // 返回实际的数据部分
          return responseData.data
        } else {
          // 业务逻辑失败
          throw new Error(responseData.message || '操作失败')
        }
      }
      // 如果不是标准格式，直接返回原数据
      return responseData
    } else if (statusCode === 401) {
      // JWT token无效或过期，清除本地token并跳转到登录页
      clearToken()
      Taro.redirectTo({
        url: '/pages/login/index'
      })
      throw new Error('登录已过期，请重新登录')
    } else {
      throw new Error(responseData?.message || '请求失败')
    }
  }).catch(error => {
    console.error('请求错误:', error)
    if (showError) {
      Taro.showToast({
        title: error.message || '网络错误',
        icon: 'none'
      })
    }
    throw error
  })
}

export default request
