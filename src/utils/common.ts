import Taro from '@tarojs/taro'

// 格式化数字，添加千分位分隔符
export const formatNumber = (num: number): string => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 格式化战力显示
export const formatCombatPower = (power: number): string => {
  if (power >= 100000000) {
    return `${(power / 100000000).toFixed(1)}亿`
  } else if (power >= 10000) {
    return `${(power / 10000).toFixed(1)}万`
  } else {
    return formatNumber(power)
  }
}

// 显示确认对话框
export const showConfirm = (options: {
  title: string
  content: string
  confirmText?: string
  cancelText?: string
}): Promise<boolean> => {
  return new Promise((resolve) => {
    Taro.showModal({
      title: options.title,
      content: options.content,
      confirmText: options.confirmText || '确定',
      cancelText: options.cancelText || '取消',
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

// 显示加载提示
export const showLoading = (title: string = '加载中...') => {
  Taro.showLoading({
    title,
    mask: true
  })
}

// 隐藏加载提示
export const hideLoading = () => {
  Taro.hideLoading()
}

// 显示成功提示
export const showSuccess = (title: string) => {
  Taro.showToast({
    title,
    icon: 'success',
    duration: 2000
  })
}

// 显示错误提示
export const showError = (title: string) => {
  Taro.showToast({
    title,
    icon: 'none',
    duration: 3000
  })
}

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let lastTime = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}

// 复制文本到剪贴板
export const copyToClipboard = (text: string): Promise<boolean> => {
  return new Promise((resolve) => {
    Taro.setClipboardData({
      data: text,
      success: () => {
        showSuccess('复制成功')
        resolve(true)
      },
      fail: () => {
        showError('复制失败')
        resolve(false)
      }
    })
  })
}

// 获取分组类型的中文名称
export const getGroupTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    'GONG_CHENG': '攻城',
    'SHOU_CHENG': '守城',
    'GUAN_DU1': '官渡1',
    'GUAN_DU2': '官渡2'
  }
  return typeMap[type] || type
}

// 获取账号状态的中文名称
export const getAccountStatusName = (status: string): string => {
  const statusMap: Record<string, string> = {
    'PENDING': '待审核',
    'APPROVED': '已通过',
    'REJECTED': '已拒绝'
  }
  return statusMap[status] || status
}

// 获取账号状态的样式类名
export const getAccountStatusClass = (status: string): string => {
  const classMap: Record<string, string> = {
    'PENDING': 'status-pending',
    'APPROVED': 'status-approved',
    'REJECTED': 'status-rejected'
  }
  return classMap[status] || ''
}
