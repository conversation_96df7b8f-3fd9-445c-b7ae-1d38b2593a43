import Taro from '@tarojs/taro'
import { userApi } from '../services/api'
import { setToken, clearToken } from '../utils/request'
import type { UserInfo } from '../types'

// 用户状态管理
class UserStore {
  private userInfo: UserInfo | null = null
  private isLoggedIn: boolean = false

  constructor() {
    this.loadUserFromStorage()
  }

  // 从本地存储加载用户信息
  private loadUserFromStorage() {
    try {
      const userInfoStr = Taro.getStorageSync('userInfo')
      if (userInfoStr) {
        this.userInfo = JSON.parse(userInfoStr)
        this.isLoggedIn = true
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)
    }
  }

  // 保存用户信息到本地存储
  private saveUserToStorage(userInfo: UserInfo) {
    try {
      Taro.setStorageSync('userInfo', JSON.stringify(userInfo))
    } catch (error) {
      console.error('保存用户信息失败:', error)
    }
  }

  // 清除用户信息
  private clearUserFromStorage() {
    try {
      Taro.removeStorageSync('userInfo')
    } catch (error) {
      console.error('清除用户信息失败:', error)
    }
  }

  // 获取用户信息
  getUserInfo(): UserInfo | null {
    return this.userInfo
  }

  // 检查是否已登录
  getIsLoggedIn(): boolean {
    return this.isLoggedIn
  }

  // 微信登录
  async wechatLogin(): Promise<boolean> {
    try {
      // 获取微信登录code
      const loginRes = await Taro.login()
      if (!loginRes.code) {
        throw new Error('获取微信登录code失败')
      }

      // 获取用户信息
      let userProfile: any = {}
      try {
        const profileRes = await Taro.getUserProfile({
          desc: '用于完善用户资料'
        })
        userProfile = profileRes.userInfo
      } catch (error) {
        console.log('用户取消授权或获取用户信息失败', error)
      }

      // 调用后端登录接口
      const loginResult = await userApi.wechatLogin({
        code: loginRes.code,
        nickName: userProfile.nickName,
        avatarUrl: userProfile.avatarUrl,
        gender: userProfile.gender,
        country: userProfile.country,
        province: userProfile.province,
        city: userProfile.city,
        language: userProfile.language
      })

      // 保存JWT token
      setToken(loginResult.token)

      this.userInfo = {
        userId: loginResult.userId,
        nickName: loginResult.nickName,
        avatarUrl: loginResult.avatarUrl
      }
      this.isLoggedIn = true
      this.saveUserToStorage(this.userInfo)
      return true
    } catch (error) {
      console.error('登录失败:', error)
      Taro.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
      return false
    }
  }

  // 检查登录状态
  async checkLoginStatus(): Promise<boolean> {
    if (!this.isLoggedIn) {
      return false
    }

    try {
      // 验证服务端登录状态（JWT token会自动在请求中发送）
      const userInfo = await userApi.getUserInfo()
      this.userInfo = userInfo
      this.saveUserToStorage(userInfo)
      return true
    } catch (error) {
      // JWT token无效或过期，清除本地状态
      this.logout()
      return false
    }
  }

  // 登出
  async logout(): Promise<void> {
    try {
      await userApi.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除JWT token
      clearToken()
      this.userInfo = null
      this.isLoggedIn = false
      this.clearUserFromStorage()
    }
  }

  // 要求登录
  requireLogin(): Promise<boolean> {
    return new Promise((resolve) => {
      if (this.isLoggedIn) {
        resolve(true)
        return
      }

      Taro.showModal({
        title: '需要登录',
        content: '请先登录后再进行操作',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            Taro.navigateTo({
              url: '/pages/login/index'
            }).then(() => {
              resolve(false)
            })
          } else {
            resolve(false)
          }
        }
      })
    })
  }
}

// 导出单例
export const userStore = new UserStore()
