package cn.flode.game.config.mybatis;

import cn.flode.game.config.mybatis.listener.InsertListener;
import cn.flode.game.config.mybatis.listener.UpdateListener;
import cn.flode.game.framework.BaseEntity;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.core.audit.AuditMessage;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class MybatisFlexConfig implements InitializingBean {

  @Override
  public void afterPropertiesSet() {
    InsertListener insertListener = new InsertListener();
    UpdateListener updateListener = new UpdateListener();
    FlexGlobalConfig config = FlexGlobalConfig.getDefaultConfig();
    config.registerInsertListener(insertListener, BaseEntity.class);
    config.registerUpdateListener(updateListener, BaseEntity.class);

    AuditManager.setAuditEnable(true);
    AuditManager.setMessageFactory(
        () -> {
          AuditMessage message = new AuditMessage();
          try {
            message.setUser(Objects.toString(SecurityUtils.currentUserId(), null));
          } catch (Exception e) {
            // 如果获取当前用户失败（如注册时），使用默认值
            message.setUser("anonymous");
          }
          return message;
        });
    AuditManager.setMessageCollector(
        auditMessage ->
            log.info(
                "user {} execSql {} spend {}ms",
                auditMessage.getUser(),
                auditMessage.getFullSql(),
                auditMessage.getElapsedTime()));
  }
}
