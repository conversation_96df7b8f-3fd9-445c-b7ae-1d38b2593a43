package cn.flode.game.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.flode.game.controller.dto.WeChatLoginDTO;
import cn.flode.game.controller.vo.LoginResult;
import cn.flode.game.entity.User;
import cn.flode.game.mapper.UserMapper;
import cn.flode.game.security.UserPrincipal;
import cn.flode.game.util.JwtUtils;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


import java.time.LocalDateTime;

/**
 * 用户服务层实现。
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService extends ServiceImpl<UserMapper, User> {

    private final WxMaService wxMaService;
    private final JwtUtils jwtUtils;

    /**
     * 微信小程序登录
     *
     * @param loginDTO 微信登录信息
     * @return 登录结果
     */
    public LoginResult weChatLogin(WeChatLoginDTO loginDTO) {
        try {
            // 1. 通过code获取openId和sessionKey
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(loginDTO.getCode());
            String openId = sessionInfo.getOpenid();
            String unionId = sessionInfo.getUnionid();

            if (!StringUtils.hasText(openId)) {
                throw new RuntimeException("获取微信用户信息失败");
            }

            // 2. 根据openId查找用户
            User existingUser = getUserByOpenId(openId);
            boolean isNewUser = false;

            if (existingUser == null) {
                // 3. 新用户，创建用户记录
                isNewUser = true;
                existingUser = User.builder()
                        .openId(openId)
                        .unionId(unionId)
                        .nickName(loginDTO.getNickName())
                        .avatarUrl(loginDTO.getAvatarUrl())
                        .gender(loginDTO.getGender())
                        .country(loginDTO.getCountry())
                        .province(loginDTO.getProvince())
                        .city(loginDTO.getCity())
                        .language(loginDTO.getLanguage())
                        .lastLoginTime(LocalDateTime.now())
                        .build();
                save(existingUser);
            } else {
                // 4. 老用户，更新用户信息和登录时间
                if (StringUtils.hasText(loginDTO.getNickName())) {
                    existingUser.setNickName(loginDTO.getNickName());
                }
                if (StringUtils.hasText(loginDTO.getAvatarUrl())) {
                    existingUser.setAvatarUrl(loginDTO.getAvatarUrl());
                }
                if (loginDTO.getGender() != null) {
                    existingUser.setGender(loginDTO.getGender());
                }
                if (StringUtils.hasText(loginDTO.getCountry())) {
                    existingUser.setCountry(loginDTO.getCountry());
                }
                if (StringUtils.hasText(loginDTO.getProvince())) {
                    existingUser.setProvince(loginDTO.getProvince());
                }
                if (StringUtils.hasText(loginDTO.getCity())) {
                    existingUser.setCity(loginDTO.getCity());
                }
                if (StringUtils.hasText(loginDTO.getLanguage())) {
                    existingUser.setLanguage(loginDTO.getLanguage());
                }
                existingUser.setLastLoginTime(LocalDateTime.now());
                updateById(existingUser);
            }

            // 5. 生成JWT token
            UserPrincipal userPrincipal = UserPrincipal.create(existingUser);
            String token = jwtUtils.generateToken(userPrincipal);

            return LoginResult.success(existingUser.getId(), existingUser.getNickName(),
                    existingUser.getAvatarUrl(), isNewUser, token);

        } catch (Exception e) {
            log.error("微信小程序登录失败", e);
            throw new RuntimeException("登录失败：" + e.getMessage());
        }
    }

    /**
     * 用户登出
     *
     * @return 登出结果
     */
    public String logout() {
        // JWT是无状态的，客户端删除token即可实现登出
        return "登出成功";
    }

    /**
     * 根据openId获取用户
     *
     * @param openId 微信openId
     * @return 用户对象，如果不存在返回null
     */
    public User getUserByOpenId(String openId) {
        return mapper.selectOneByQuery(
                QueryWrapper.create().eq(User::getOpenId, openId)
        );
    }
}
