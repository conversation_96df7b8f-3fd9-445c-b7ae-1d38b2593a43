.index {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 30px;

  .header {
    text-align: center;
    margin-bottom: 60px;

    .title {
      display: block;
      font-size: 48px;
      font-weight: bold;
      color: #ffffff;
      margin-bottom: 20px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .subtitle {
      display: block;
      font-size: 28px;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .game-list {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }

  .game-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .game-image {
      width: 120px;
      height: 120px;
      border-radius: 15px;
      margin-right: 30px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;

      .game-icon {
        font-size: 60px;
      }
    }

    .game-info {
      flex: 1;

      .game-name {
        display: block;
        font-size: 32px;
        font-weight: bold;
        color: #333333;
        margin-bottom: 15px;
      }

      .game-desc {
        display: block;
        font-size: 24px;
        color: #666666;
        line-height: 1.4;
      }
    }

    .game-arrow {
      .arrow-text {
        font-size: 24px;
        color: #667eea;
        font-weight: 500;
      }
    }
  }
}