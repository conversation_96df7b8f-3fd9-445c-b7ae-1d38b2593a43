import { View, Text } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import { userStore } from '../../store/user'
import type { GameInfo } from '../../types'
import './index.scss'

// 游戏列表数据
const gameList: GameInfo[] = [
  {
    id: 'sanbing',
    name: '三国冰河天下',
    description: '策略战争游戏，建立联盟，征战天下',
    image: '/images/sanbing-cover.jpg',
    route: '/pages/sanbing/index'
  }
  // 可以添加更多游戏
]

export default function Index() {
  useLoad(() => {
    console.log('Page loaded.')
  })

  // 处理游戏卡片点击
  const handleGameClick = async (game: GameInfo) => {
    // 检查用户是否已登录
    const isLoggedIn = await userStore.checkLoginStatus()

    if (!isLoggedIn) {
      // 用户未登录，提示登录
      const loginRequired = await userStore.requireLogin()
      if (!loginRequired) {
        return
      }
    } else {
      // 用户已登录，直接跳转到游戏页面
      Taro.navigateTo({
        url: game.route
      })
    }
  }

  return (
    <View className='index'>
      <View className='header'>
        <Text className='title'>游戏战术室</Text>
        <Text className='subtitle'>选择你的游戏</Text>
      </View>

      <View className='game-list'>
        {gameList.map(game => (
          <View
            key={game.id}
            className='game-card'
            onClick={() => handleGameClick(game)}
          >
            <View className='game-image'>
              <Text className='game-icon'>🏰</Text>
            </View>
            <View className='game-info'>
              <Text className='game-name'>{game.name}</Text>
              <Text className='game-desc'>{game.description}</Text>
            </View>
            <View className='game-arrow'>
              <Text className='arrow-text'>进入游戏</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  )
}
