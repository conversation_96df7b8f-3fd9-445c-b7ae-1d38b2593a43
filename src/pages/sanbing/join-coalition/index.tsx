import { View, Text, Input, Button } from '@tarojs/components'
import { useLoad, useRouter } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import { useState } from 'react'
import { sanBingApi } from '../../../services/api'
import './index.scss'

export default function JoinCoalition() {
  const router = useRouter()
  const accountId = router.params.accountId
  
  const [coalitionCode, setCoalitionCode] = useState('')
  const [level, setLevel] = useState('')
  const [loading, setLoading] = useState(false)

  useLoad(() => {
    console.log('JoinCoalition page loaded.')
    if (!accountId) {
      Taro.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)
    }
  })

  // 处理联盟代码输入
  const handleCoalitionCodeChange = (e: any) => {
    setCoalitionCode(e.detail.value)
  }

  // 处理等级输入
  const handleLevelChange = (e: any) => {
    const value = e.detail.value
    // 只允许输入1-5的数字
    if (/^[1-5]?$/.test(value)) {
      setLevel(value)
    }
  }

  // 提交申请
  const handleSubmit = async () => {
    if (!accountId) {
      Taro.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)
      return
    }

    if (!coalitionCode.trim()) {
      Taro.showToast({
        title: '请输入联盟代码',
        icon: 'none'
      })
      return
    }

    if (!level || parseInt(level) < 1 || parseInt(level) > 5) {
      Taro.showToast({
        title: '请输入正确的联盟阶位(1-5)',
        icon: 'none'
      })
      return
    }

    setLoading(true)
    try {
      const result = await sanBingApi.account.joinCoalition({
        accountId,
        coalitionCode: coalitionCode.trim(),
        level: parseInt(level)
      })

      if (result.success) {
        Taro.showToast({
          title: '申请提交成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          Taro.navigateBack()
        }, 1500)
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('申请加入联盟失败:', error)
      Taro.showToast({
        title: error.message || '申请失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 取消操作
  const handleCancel = () => {
    Taro.navigateBack()
  }

  return (
    <View className='join-coalition'>
      <View className='container'>
        <View className='header'>
          <Text className='title'>申请加入联盟</Text>
          <Text className='subtitle'>请输入联盟信息</Text>
        </View>

        <View className='form'>
          <View className='form-item'>
            <Text className='label'>联盟代码</Text>
            <Input
              className='input'
              placeholder='请输入联盟代码'
              value={coalitionCode}
              onInput={handleCoalitionCodeChange}
              maxlength={10}
            />
            <Text className='hint'>联盟代码由联盟管理员提供</Text>
          </View>

          <View className='form-item'>
            <Text className='label'>联盟阶位</Text>
            <Input
              className='input'
              placeholder='请输入阶位(1-5)'
              value={level}
              onInput={handleLevelChange}
              type='number'
              maxlength={1}
            />
            <Text className='hint'>请输入你在联盟中的阶位等级</Text>
          </View>
        </View>

        <View className='actions'>
          <Button 
            className='cancel-btn'
            onClick={handleCancel}
            disabled={loading}
          >
            取消
          </Button>
          <Button 
            className='submit-btn'
            onClick={handleSubmit}
            loading={loading}
            disabled={loading}
          >
            {loading ? '申请中...' : '提交申请'}
          </Button>
        </View>

        <View className='tips'>
          <Text className='tips-text'>
            提交申请后，需要等待联盟管理员审核通过才能正式加入联盟
          </Text>
        </View>
      </View>
    </View>
  )
}
