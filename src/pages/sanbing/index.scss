.sanbing {
  min-height: 100vh;
  background: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;

  .scroll-container {
    height: 100vh;
  }

  .header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 60px 30px 40px;
    text-align: center;
    color: #ffffff;

    .title {
      display: block;
      font-size: 42px;
      font-weight: bold;
      margin-bottom: 15px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      line-height: 1.3;
      letter-spacing: 1px;
    }

    .subtitle {
      display: block;
      font-size: 26px;
      opacity: 0.9;
      line-height: 1.4;
      letter-spacing: 0.5px;
    }
  }

  .quick-actions {
    padding: 30px;
    display: flex;
    gap: 20px;

    .action-btn {
      flex: 1;
      height: 80px;
      font-size: 28px;
      font-weight: 500;
      line-height: 1.4;
      letter-spacing: 0.5px;

      // 确保中文显示和谐
      font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;

      &.create-coalition {
        // 使用 Taro 的 primary 类型，可以通过 CSS 变量自定义颜色
        --button-color: #ffffff;
        --button-background-color: #667eea;
        --button-border-color: #667eea;
        border-radius: 15px;
      }

      &.create-account {
        // 使用 Taro 的 default 类型，自定义样式
        --button-color: #ffffff;
        --button-background-color: #764ba2;
        --button-border-color: #764ba2;
        border-radius: 15px;
      }
    }
  }

  .section {
    margin: 0 30px 40px;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .section-title {
        font-size: 32px;
        font-weight: bold;
        color: #333333;
        line-height: 1.3;
        letter-spacing: 0.5px;
      }

      .section-count {
        font-size: 24px;
        color: #999999;
        margin-left: 10px;
        line-height: 1.4;
      }
    }

    .coalition-list,
    .account-list {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .account-areas {
      display: flex;
      flex-direction: column;
      gap: 25px;
    }

    .area-section {
      .area-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding-left: 10px;

        .area-title {
          font-size: 28px;
          font-weight: bold;
          color: #667eea;
          line-height: 1.3;
          letter-spacing: 0.5px;
        }

        .area-count {
          font-size: 22px;
          color: #999999;
          margin-left: 10px;
          line-height: 1.4;
        }
      }
    }

    .coalition-item,
    .account-item {
      background: #ffffff;
      border-radius: 15px;
      padding: 25px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      display: flex;
      align-items: center;
      justify-content: space-between;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
      }
    }

    .coalition-info,
    .account-info {
      flex: 1;

      .coalition-name,
      .account-name {
        display: block;
        font-size: 30px;
        font-weight: bold;
        color: #333333;
        margin-bottom: 8px;
        line-height: 1.3;
        letter-spacing: 0.5px;
      }

      .coalition-code,
      .coalition-area,
      .coalition-members,
      .coalition-pending,
      .account-level,
      .account-power {
        display: block;
        font-size: 24px;
        color: #666666;
        margin-bottom: 5px;
        line-height: 1.4;
        letter-spacing: 0.3px;
      }

      .coalition-pending {
        color: #ff9500;
        font-weight: 500;
      }
    }

    .coalition-badge {
      .manager-badge {
        background: #ff6b6b;
        color: #ffffff;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 20px;
        font-weight: 500;
      }
    }

    .account-status {
      text-align: right;

      .joined-coalition {
        .coalition-name {
          font-size: 24px;
          color: #667eea;
          font-weight: 500;
          margin-bottom: 5px;
        }

        .status-text {
          font-size: 20px;
          color: #52c41a;
        }
      }

      .not-joined {
        font-size: 24px;
        color: #ff6b6b;
        font-weight: 500;
      }
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      background: #ffffff;
      border-radius: 15px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

      .empty-text {
        display: block;
        font-size: 28px;
        color: #999999;
        margin-bottom: 10px;
      }

      .empty-desc {
        display: block;
        font-size: 24px;
        color: #cccccc;
      }
    }
  }
}
