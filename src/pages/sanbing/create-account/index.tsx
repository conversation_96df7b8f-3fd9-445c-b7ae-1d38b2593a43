import { View, Text, Input, Button } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import { useState } from 'react'
import { sanBingApi } from '../../../services/api'
import './index.scss'

export default function CreateAccount() {
  const [name, setName] = useState('')
  const [areaCode, setAreaCode] = useState('')
  const [combatPower, setCombatPower] = useState('')
  const [addition, setAddition] = useState('')
  const [gatheringCapacity, setGatheringCapacity] = useState('')
  const [loading, setLoading] = useState(false)

  useLoad(() => {
    console.log('CreateAccount page loaded.')
  })

  // 处理账号名称输入
  const handleNameChange = (e: any) => {
    setName(e.detail.value)
  }

  // 处理区域代码输入
  const handleAreaCodeChange = (e: any) => {
    const value = e.detail.value
    if (/^\d*$/.test(value)) {
      setAreaCode(value)
    }
  }

  // 处理战力输入
  const handleCombatPowerChange = (e: any) => {
    const value = e.detail.value
    if (/^\d*$/.test(value)) {
      setCombatPower(value)
    }
  }

  // 处理加成输入
  const handleAdditionChange = (e: any) => {
    const value = e.detail.value
    if (/^\d*\.?\d*$/.test(value)) {
      setAddition(value)
    }
  }

  // 处理集结容量输入
  const handleGatheringCapacityChange = (e: any) => {
    const value = e.detail.value
    if (/^\d*$/.test(value)) {
      setGatheringCapacity(value)
    }
  }

  // 提交创建
  const handleSubmit = async () => {
    if (!name.trim()) {
      Taro.showToast({
        title: '请输入账号名称',
        icon: 'none'
      })
      return
    }

    if (!areaCode || parseInt(areaCode) < 1) {
      Taro.showToast({
        title: '请输入正确的区域代码',
        icon: 'none'
      })
      return
    }

    if (!combatPower || parseInt(combatPower) < 0) {
      Taro.showToast({
        title: '请输入正确的战力',
        icon: 'none'
      })
      return
    }

    setLoading(true)
    try {
      const data: any = {
        name: name.trim(),
        areaCode: parseInt(areaCode),
        combatPower: parseInt(combatPower)
      }

      if (addition && parseFloat(addition) > 0) {
        data.addition = parseFloat(addition)
      }

      if (gatheringCapacity && parseInt(gatheringCapacity) > 0) {
        data.gatheringCapacity = parseInt(gatheringCapacity)
      }

      const result = await sanBingApi.account.create(data)

      Taro.showToast({
        title: '账号创建成功',
        icon: 'success'
      })

      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)
    } catch (error) {
      console.error('创建账号失败:', error)
      Taro.showToast({
        title: error.message || '创建失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 取消操作
  const handleCancel = () => {
    Taro.navigateBack()
  }

  return (
    <View className='create-account'>
      <View className='container'>
        <View className='header'>
          <Text className='title'>创建账号</Text>
          <Text className='subtitle'>创建你的游戏账号</Text>
        </View>

        <View className='form'>
          <View className='form-item'>
            <Text className='label'>账号名称 *</Text>
            <Input
              className='input'
              placeholder='请输入账号名称'
              value={name}
              onInput={handleNameChange}
              maxlength={20}
            />
          </View>

          <View className='form-item'>
            <Text className='label'>区域代码 *</Text>
            <Input
              className='input'
              placeholder='请输入区域代码'
              value={areaCode}
              onInput={handleAreaCodeChange}
              type='number'
            />
          </View>

          <View className='form-item'>
            <Text className='label'>战力 *</Text>
            <Input
              className='input'
              placeholder='请输入战力'
              value={combatPower}
              onInput={handleCombatPowerChange}
              type='number'
            />
          </View>

          <View className='form-item'>
            <Text className='label'>加成</Text>
            <Input
              className='input'
              placeholder='请输入加成(可选)'
              value={addition}
              onInput={handleAdditionChange}
              type='digit'
            />
          </View>

          <View className='form-item'>
            <Text className='label'>集结容量</Text>
            <Input
              className='input'
              placeholder='请输入集结容量(可选)'
              value={gatheringCapacity}
              onInput={handleGatheringCapacityChange}
              type='number'
            />
          </View>
        </View>

        <View className='actions'>
          <Button 
            className='cancel-btn'
            onClick={handleCancel}
            disabled={loading}
          >
            取消
          </Button>
          <Button 
            className='submit-btn'
            onClick={handleSubmit}
            loading={loading}
            disabled={loading}
          >
            {loading ? '创建中...' : '创建账号'}
          </Button>
        </View>

        <View className='tips'>
          <Text className='tips-text'>
            每个用户在同一区域最多可以创建2个账号
          </Text>
        </View>
      </View>
    </View>
  )
}
