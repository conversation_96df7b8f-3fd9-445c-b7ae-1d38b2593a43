.create-account {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 40px 30px;

  .container {
    max-width: 600px;
    margin: 0 auto;
  }

  .header {
    text-align: center;
    margin-bottom: 60px;

    .title {
      display: block;
      font-size: 42px;
      font-weight: bold;
      color: #333333;
      margin-bottom: 15px;
    }

    .subtitle {
      display: block;
      font-size: 26px;
      color: #666666;
    }
  }

  .form {
    background: #ffffff;
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 40px;

    .form-item {
      margin-bottom: 30px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        display: block;
        font-size: 28px;
        font-weight: 500;
        color: #333333;
        margin-bottom: 15px;
      }

      .input {
        width: 100%;
        height: 80px;
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 0 20px;
        font-size: 28px;
        color: #333333;
        transition: all 0.3s ease;

        &:focus {
          border-color: #667eea;
          background: #ffffff;
        }
      }
    }
  }

  .actions {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;

    .cancel-btn,
    .submit-btn {
      flex: 1;
      height: 88px;
      border-radius: 15px;
      font-size: 30px;
      font-weight: 500;
      border: none;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }
    }

    .cancel-btn {
      background: #f8f9fa;
      color: #666666;
      border: 2px solid #e9ecef;

      &:active {
        background: #e9ecef;
      }
    }

    .submit-btn {
      background: #764ba2;
      color: #ffffff;
      box-shadow: 0 4px 16px rgba(118, 75, 162, 0.3);

      &:active {
        box-shadow: 0 2px 8px rgba(118, 75, 162, 0.4);
      }

      &.button-loading {
        background: rgba(118, 75, 162, 0.7);
      }
    }
  }

  .tips {
    background: rgba(118, 75, 162, 0.1);
    border-radius: 15px;
    padding: 25px;
    border-left: 4px solid #764ba2;

    .tips-text {
      font-size: 24px;
      color: #764ba2;
      line-height: 1.5;
    }
  }
}
