import { View, Text, Input, Button } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import { useState } from 'react'
import { sanBingApi } from '../../../services/api'
import './index.scss'

export default function CreateCoalition() {
  const [name, setName] = useState('')
  const [areaCode, setAreaCode] = useState('')
  const [loading, setLoading] = useState(false)

  useLoad(() => {
    console.log('CreateCoalition page loaded.')
  })

  // 处理联盟名称输入
  const handleNameChange = (e: any) => {
    setName(e.detail.value)
  }

  // 处理区域代码输入
  const handleAreaCodeChange = (e: any) => {
    const value = e.detail.value
    // 只允许输入数字
    if (/^\d*$/.test(value)) {
      setAreaCode(value)
    }
  }

  // 提交创建
  const handleSubmit = async () => {
    if (!name.trim()) {
      Taro.showToast({
        title: '请输入联盟名称',
        icon: 'none'
      })
      return
    }

    if (!areaCode || parseInt(areaCode) < 1) {
      Taro.showToast({
        title: '请输入正确的区域代码',
        icon: 'none'
      })
      return
    }

    setLoading(true)
    try {
      const result = await sanBingApi.coalition.create({
        name: name.trim(),
        areaCode: parseInt(areaCode)
      })

      Taro.showToast({
        title: '联盟创建成功',
        icon: 'success'
      })

      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)
    } catch (error) {
      console.error('创建联盟失败:', error)
      Taro.showToast({
        title: error.message || '创建失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 取消操作
  const handleCancel = () => {
    Taro.navigateBack()
  }

  return (
    <View className='create-coalition'>
      <View className='container'>
        <View className='header'>
          <Text className='title'>创建联盟</Text>
          <Text className='subtitle'>建立你的游戏联盟</Text>
        </View>

        <View className='form'>
          <View className='form-item'>
            <Text className='label'>联盟名称</Text>
            <Input
              className='input'
              placeholder='请输入联盟名称'
              value={name}
              onInput={handleNameChange}
              maxlength={20}
            />
            <Text className='hint'>联盟名称最多20个字符</Text>
          </View>

          <View className='form-item'>
            <Text className='label'>区域代码</Text>
            <Input
              className='input'
              placeholder='请输入区域代码'
              value={areaCode}
              onInput={handleAreaCodeChange}
              type='number'
            />
            <Text className='hint'>请输入游戏区服编号</Text>
          </View>
        </View>

        <View className='actions'>
          <Button 
            className='cancel-btn'
            onClick={handleCancel}
            disabled={loading}
          >
            取消
          </Button>
          <Button 
            className='submit-btn'
            onClick={handleSubmit}
            loading={loading}
            disabled={loading}
          >
            {loading ? '创建中...' : '创建联盟'}
          </Button>
        </View>

        <View className='tips'>
          <Text className='tips-text'>
            创建联盟后，你将成为联盟管理员，可以管理联盟成员和分组
          </Text>
        </View>
      </View>
    </View>
  )
}
