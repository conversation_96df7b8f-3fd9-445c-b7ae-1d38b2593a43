.coalition-detail {
  min-height: 100vh;
  background: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;

  &.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: #999999;
  }

  .scroll-container {
    height: 100vh;
  }

  .coalition-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40px 30px;
    color: #ffffff;

    .coalition-info {
      position: relative;

      .coalition-name {
        display: block;
        font-size: 42px;
        font-weight: bold;
        margin-bottom: 15px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        line-height: 1.3;
        letter-spacing: 1px;
      }

      .coalition-code,
      .coalition-area {
        display: block;
        font-size: 26px;
        opacity: 0.9;
        margin-bottom: 8px;
        line-height: 1.4;
        letter-spacing: 0.5px;
      }

      .manager-badge {
        position: absolute;
        top: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 8px 16px;
        backdrop-filter: blur(10px);

        .badge-text {
          font-size: 22px;
          font-weight: 500;
          line-height: 1.2;
          letter-spacing: 0.5px;
        }
      }
    }

    .manager-actions {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;

      .action-btn {
        --button-color: #667eea;
        --button-background-color: rgba(255, 255, 255, 0.9);
        --button-border-color: rgba(255, 255, 255, 0.9);
        font-size: 22px;
        font-weight: 500;
        border-radius: 20px;
        min-width: 80px;
        height: 60px;
        line-height: 1.2;
        letter-spacing: 0.5px;
      }
    }
  }

  .section {
    margin: 30px;

    .section-header {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 15px;

      .section-title {
        font-size: 32px;
        font-weight: bold;
        color: #333333;
        line-height: 1.3;
        letter-spacing: 0.5px;
      }

      .section-count {
        font-size: 24px;
        color: #999999;
        margin-left: 10px;
        line-height: 1.4;
      }

      .section-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;

        .create-group-btn,
        .apply-group-btn {
          font-size: 20px;
          font-weight: 500;
          border-radius: 18px;
          min-width: 70px;
          height: 50px;
          line-height: 1.2;
          letter-spacing: 0.5px;

          &.create-group-btn {
            --button-color: #ffffff;
            --button-background-color: #52c41a;
            --button-border-color: #52c41a;
          }

          &.apply-group-btn {
            --button-color: #ffffff;
            --button-background-color: #1890ff;
            --button-border-color: #1890ff;
          }
        }
      }
    }

    .groups-container,
    .apply-accounts-container,
    .pending-accounts-container {
      .group-type-section,
      .apply-type-section {
        margin-bottom: 30px;

        .group-type-title,
        .apply-type-title {
          font-size: 28px;
          font-weight: 600;
          color: #667eea;
          margin-bottom: 15px;
          padding-left: 15px;
          border-left: 4px solid #667eea;
          line-height: 1.3;
          letter-spacing: 0.5px;
        }

        .group-item {
          background: #ffffff;
          border-radius: 15px;
          padding: 25px;
          box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
          margin-bottom: 15px;

          .group-header {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .group-name {
              display: block;
              font-size: 30px;
              font-weight: bold;
              color: #333333;
              margin-bottom: 8px;
              line-height: 1.3;
              letter-spacing: 0.5px;
            }

            .group-task {
              display: block;
              font-size: 24px;
              color: #666666;
              line-height: 1.4;
              letter-spacing: 0.3px;
            }

            .group-actions {
              display: flex;
              gap: 8px;

              button {
                font-size: 20px;
                font-weight: 500;
                border-radius: 15px;
                min-width: 50px;
                height: 45px;
                line-height: 1.2;
                letter-spacing: 0.5px;

                --button-color: #666666;
                --button-background-color: #f5f5f5;
                --button-border-color: #d9d9d9;
              }
            }
          }

          .group-accounts {
            border-top: 1px solid #f0f0f0;
            padding-top: 20px;
          }
        }
      }
    }

    .account-item {
      background: #ffffff;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0 1px 8px rgba(0, 0, 0, 0.06);

      &:last-child {
        margin-bottom: 0;
      }

      &.clickable {
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 12px rgba(102, 126, 234, 0.15);
        }

        &:active {
          transform: translateY(0);
        }
      }

      .account-info {
        flex: 1;

        .account-name {
          display: block;
          font-size: 28px;
          font-weight: bold;
          color: #333333;
          margin-bottom: 5px;
          line-height: 1.3;
          letter-spacing: 0.5px;
        }

        .account-details {
          display: block;
          font-size: 22px;
          color: #666666;
          line-height: 1.4;
          letter-spacing: 0.3px;
        }
      }

      .account-status {
        .status-text {
          font-size: 22px;
          font-weight: 500;
          padding: 6px 12px;
          border-radius: 15px;
        }

        &.status-pending .status-text {
          background: #fff7e6;
          color: #fa8c16;
        }

        &.status-approved .status-text {
          background: #f6ffed;
          color: #52c41a;
        }

        &.status-rejected .status-text {
          background: #fff2f0;
          color: #ff4d4f;
        }
      }

      // 待审核账号特殊样式
      &.pending-account-item {
        cursor: pointer;
        transition: all 0.2s ease;
        border: 2px solid transparent;

        &:hover {
          border-color: #667eea;
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15);
        }

        .account-info {
          .account-addition,
          .account-gathering {
            display: block;
            font-size: 20px;
            color: #52c41a;
            margin-top: 3px;
            line-height: 1.4;
          }
        }

        .account-actions {
          display: flex;
          gap: 10px;
          align-items: center;

          .approve-btn,
          .reject-btn {
            font-size: 22px;
            font-weight: 500;
            border-radius: 20px;
            min-width: 60px;
            height: 60px;
            line-height: 1.2;
            letter-spacing: 0.5px;
          }

          .approve-btn {
            --button-color: #ffffff;
            --button-background-color: #52c41a;
            --button-border-color: #52c41a;
          }

          .reject-btn {
            --button-color: #666666;
            --button-background-color: #f5f5f5;
            --button-border-color: #d9d9d9;
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      background: #ffffff;
      border-radius: 15px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

      .empty-text {
        font-size: 28px;
        color: #999999;
        line-height: 1.4;
        letter-spacing: 0.5px;
      }
    }
  }
}
