import { View, Text, ScrollView, But<PERSON> } from '@tarojs/components'
import { useLoad, useRouter, useDidShow } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import { useState, useCallback } from 'react'
import { sanBingApi } from '../../../services/api'
import { getGroupTypeName, getAccountStatusName, getAccountStatusClass, formatCombatPower } from '../../../utils/common'
import type { CoalitionDetailInfo, GroupInfo, AccountInfo } from '../../../types'
import './index.scss'

export default function CoalitionDetail() {
  const router = useRouter()
  const coalitionId = router.params.coalitionId
  const source = router.params.source // 'my-coalition' 或 'my-account'
  const accountId = router.params.accountId // 从我的账号进入时的账号ID

  const [coalitionDetail, setCoalitionDetail] = useState<CoalitionDetailInfo | null>(null)
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // 判断是否为管理员模式（从我的联盟进入）
  const isManagerMode = source === 'my-coalition' && coalitionDetail?.isManager
  // 判断是否为成员模式（从我的账号进入）
  const isMemberMode = source === 'my-account'

  useLoad(() => {
    console.log('CoalitionDetail page loaded.')
    if (!coalitionId) {
      Taro.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)
    }
  })

  useDidShow(() => {
    loadCoalitionDetail()
  })

  // 加载联盟详情
  const loadCoalitionDetail = useCallback(async () => {
    if (loading || !coalitionId) return

    setLoading(true)
    try {
      const detail = await sanBingApi.coalition.getDetail(coalitionId)
      setCoalitionDetail(detail)
    } catch (error) {
      console.error('加载联盟详情失败:', error)
      Taro.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }, [coalitionId])

  // 下拉刷新
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    await loadCoalitionDetail()
    setRefreshing(false)
  }, [loadCoalitionDetail])

  // 审核账号申请
  const handleApproveAccount = useCallback(async (accountId: string, approved: boolean) => {
    try {
      await sanBingApi.account.approve({
        accountId,
        approved
      })

      Taro.showToast({
        title: approved ? '审核通过' : '审核拒绝',
        icon: 'success'
      })

      // 刷新联盟详情
      await loadCoalitionDetail()
    } catch (error) {
      console.error('审核账号失败:', error)
      Taro.showToast({
        title: error.message || '审核失败',
        icon: 'none'
      })
    }
  }, [loadCoalitionDetail])

  // 创建分组
  const handleCreateGroup = useCallback(async (type: 'GUAN_DU1' | 'GUAN_DU2' | 'GONG_CHENG') => {
    if (!coalitionDetail || !isManagerMode) return

    try {
      // 使用简单的确认对话框，分组名称使用默认格式
      const typeName = type === 'GUAN_DU1' ? '官渡一' : type === 'GUAN_DU2' ? '官渡二' : '攻城'
      const defaultName = `${typeName}分组${Date.now().toString().slice(-4)}`

      const confirmed = await new Promise<boolean>((resolve) => {
        Taro.showModal({
          title: `创建${typeName}分组`,
          content: `将创建名为"${defaultName}"的分组`,
          success: (res) => {
            resolve(res.confirm)
          }
        })
      })

      if (!confirmed) return

      await sanBingApi.group.create({
        coalitionId: coalitionDetail.coalitionId,
        name: defaultName,
        type
      })

      Taro.showToast({
        title: '分组创建成功',
        icon: 'success'
      })

      await loadCoalitionDetail()
    } catch (error) {
      console.error('创建分组失败:', error)
      Taro.showToast({
        title: error.message || '创建失败',
        icon: 'none'
      })
    }
  }, [coalitionDetail, isManagerMode, loadCoalitionDetail])

  // 申请加入分组类型（成员模式）
  const handleApplyGroupType = useCallback(async (groupType: 'GUAN_DU1' | 'GUAN_DU2' | 'GONG_CHENG') => {
    if (!accountId || !isMemberMode) return

    try {
      await sanBingApi.account.applyGroup({
        accountId,
        groupType
      })

      Taro.showToast({
        title: '申请提交成功',
        icon: 'success'
      })

      await loadCoalitionDetail()
    } catch (error) {
      console.error('申请加入分组失败:', error)
      Taro.showToast({
        title: error.message || '申请失败',
        icon: 'none'
      })
    }
  }, [accountId, isMemberMode, loadCoalitionDetail])

  // 点击分组中的账号（管理员模式）
  const handleGroupAccountClick = useCallback((account: AccountInfo, groupType: string) => {
    if (!isManagerMode) return

    const actions: string[] = []

    if (groupType === 'GUAN_DU1') {
      actions.push('申请到官渡二', '移动到官渡一其他分组')
    } else if (groupType === 'GUAN_DU2') {
      actions.push('申请到官渡一', '移动到官渡二其他分组')
    }

    if (actions.length === 0) return

    Taro.showActionSheet({
      itemList: actions,
      success: (res) => {
        // TODO: 实现具体的移动逻辑
        console.log('选择操作:', actions[res.tapIndex], account)
      }
    })
  }, [isManagerMode])

  // 点击待审核账号处理
  const handlePendingAccountClick = useCallback((account: AccountInfo) => {
    if (!isManagerMode) {
      return
    }

    Taro.showActionSheet({
      itemList: ['通过申请', '拒绝申请'],
      success: (res) => {
        if (res.tapIndex === 0) {
          handleApproveAccount(account.accountId, true)
        } else if (res.tapIndex === 1) {
          handleApproveAccount(account.accountId, false)
        }
      }
    })
  }, [isManagerMode, handleApproveAccount])



  if (!coalitionDetail) {
    return (
      <View className='coalition-detail loading'>
        <Text>加载中...</Text>
      </View>
    )
  }

  return (
    <View className='coalition-detail'>
      <ScrollView
        className='scroll-container'
        scrollY
        refresherEnabled
        refresherTriggered={refreshing}
        onRefresherRefresh={handleRefresh}
      >
        {/* 联盟基本信息 */}
        <View className='coalition-header'>
          <View className='coalition-info'>
            <Text className='coalition-name'>{coalitionDetail.name}</Text>
            <Text className='coalition-code'>代码: {coalitionDetail.code}</Text>
            <Text className='coalition-area'>区服: {coalitionDetail.areaCode}区</Text>
            {coalitionDetail.isManager && (
              <View className='manager-badge'>
                <Text>管理员</Text>
              </View>
            )}
          </View>

          {/* 管理员操作按钮 */}
          {isManagerMode && (
            <View className='manager-actions'>
              <Button
                type='default'
                size='mini'
                onClick={() => {
                  Taro.showToast({
                    title: '编辑联盟功能开发中',
                    icon: 'none'
                  })
                }}
              >
                编辑联盟
              </Button>
            </View>
          )}
        </View>

        {/* 联盟战事 */}
        <View className='section'>
          <View className='section-header'>
            <Text className='section-title'>联盟战事</Text>
          </View>
          {Object.keys(coalitionDetail.groupsByType).length > 0 ? (
            <View className='groups-container'>
              {Object.entries(coalitionDetail.groupsByType).map(([type, groups]) => (
                <View key={type} className='group-type-section'>
                  <Text className='group-type-title'>{getGroupTypeName(type)}</Text>
                  <View className='section-actions'>
                    {isManagerMode && <Button
                      type='primary'
                      size='mini'
                      onClick={() => handleCreateGroup(type as any)}
                    >
                      新建分组
                    </Button>}
                    <Button
                      type='primary'
                      size='mini'
                      onClick={() => handleApplyGroupType(type as any)}
                    >
                      申请{getGroupTypeName(type)}
                    </Button>
                  </View>

                  {groups.map((group: GroupInfo) => (
                    <View key={group.groupId} className='group-item'>
                      <View className='group-header'>
                        <Text className='group-name'>{group.name}</Text>
                        {group.task && (
                          <Text className='group-task'>任务: {group.task}</Text>
                        )}
                        {isManagerMode && (
                          <View className='group-actions'>
                            <Button
                              type='default'
                              size='mini'
                              onClick={() => {
                                // TODO: 编辑分组
                                console.log('编辑分组:', group)
                              }}
                            >
                              编辑
                            </Button>
                          </View>
                        )}
                      </View>

                      {group.accounts && group.accounts.length > 0 && (
                        <View className='group-accounts'>
                          {group.accounts.map((account: AccountInfo) => (
                            <View
                              key={account.accountId}
                              className={`account-item ${isManagerMode ? 'clickable' : ''}`}
                              onClick={() => isManagerMode && handleGroupAccountClick(account, type)}
                            >
                              <View className='account-info'>
                                <Text className='account-name'>{account.name}</Text>
                                <Text className='account-details'>
                                  等级{account.level} | 战力{formatCombatPower(account.combatPower)}
                                </Text>
                              </View>
                              <View className={`account-status ${getAccountStatusClass(account.status)}`}>
                                <Text className='status-text'>
                                  {getAccountStatusName(account.status)}
                                </Text>
                              </View>
                            </View>
                          ))}
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              ))}
            </View>
          ) : (
            <View className='empty-state'>
              <Text className='empty-text'>暂无分组</Text>
              {isManagerMode && (
                <Text className='empty-desc'>点击上方按钮创建分组</Text>
              )}
              {isMemberMode && (
                <Text className='empty-desc'>点击上方按钮申请加入战事</Text>
              )}
            </View>
          )}
        </View>

        {/* 申请中的账号 */}
        {Object.keys(coalitionDetail.applyAccountsByType).length > 0 && (
          <View className='section'>
            <View className='section-header'>
              <Text className='section-title'>申请中的账号</Text>
            </View>

            <View className='apply-accounts-container'>
              {Object.entries(coalitionDetail.applyAccountsByType).map(([type, accounts]) => (
                <View key={type} className='apply-type-section'>
                  <Text className='apply-type-title'>申请加入: {getGroupTypeName(type)}</Text>

                  {accounts.map((account: AccountInfo) => (
                    <View key={account.accountId} className='account-item'>
                      <View className='account-info'>
                        <Text className='account-name'>{account.name}</Text>
                        <Text className='account-details'>
                          等级{account.level} | 战力{formatCombatPower(account.combatPower)}
                        </Text>
                      </View>
                      <View className='account-status status-pending'>
                        <Text className='status-text'>待审核</Text>
                      </View>
                    </View>
                  ))}
                </View>
              ))}
            </View>
          </View>
        )}

        {/* 待审核账号 */}
        {isManagerMode && coalitionDetail.pendingAccounts && coalitionDetail.pendingAccounts.length > 0 && (
          <View className='section'>
            <View className='section-header'>
              <Text className='section-title'>待审核账号</Text>
              <Text className='section-count'>({coalitionDetail.pendingAccounts.length})</Text>
            </View>

            <View className='pending-accounts-container'>
              {coalitionDetail.pendingAccounts.map((account: AccountInfo) => (
                <View
                  key={account.accountId}
                  className='account-item pending-account-item'
                  onClick={() => handlePendingAccountClick(account)}
                >
                  <View className='account-info'>
                    <Text className='account-name'>{account.name}</Text>
                    <Text className='account-details'>
                      等级{account.level} | 战力{formatCombatPower(account.combatPower)}
                    </Text>
                    {account.addition && (
                      <Text className='account-addition'>加成: {account.addition}%</Text>
                    )}
                    {account.gatheringCapacity && (
                      <Text className='account-gathering'>采集: {formatCombatPower(account.gatheringCapacity)}</Text>
                    )}
                  </View>
                  <View className='account-actions'>
                    <Button
                      type='primary'
                      size='mini'
                      className='approve-btn'
                      onClick={(e) => {
                        e.stopPropagation()
                        handleApproveAccount(account.accountId, true)
                      }}
                    >
                      通过
                    </Button>
                    <Button
                      type='default'
                      size='mini'
                      className='reject-btn'
                      onClick={(e) => {
                        e.stopPropagation()
                        handleApproveAccount(account.accountId, false)
                      }}
                    >
                      拒绝
                    </Button>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  )
}
