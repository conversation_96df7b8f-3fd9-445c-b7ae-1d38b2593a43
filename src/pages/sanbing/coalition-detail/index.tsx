import { View, Text, ScrollView, Button } from '@tarojs/components'
import { useLoad, useRouter, useDidShow } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import { useState, useCallback } from 'react'
import { sanBingApi } from '../../../services/api'
import { getGroupTypeName, getAccountStatusName, getAccountStatusClass, formatCombatPower } from '../../../utils/common'
import type { CoalitionDetailInfo, GroupInfo, AccountInfo } from '../../../types'
import './index.scss'

export default function CoalitionDetail() {
  const router = useRouter()
  const coalitionId = router.params.coalitionId
  const source = router.params.source // 'my-coalition' 或 'my-account'
  const accountId = router.params.accountId // 从我的账号进入时的账号ID

  const [coalitionDetail, setCoalitionDetail] = useState<CoalitionDetailInfo | null>(null)
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // 判断是否为管理员（无论从哪个入口进入，只要是联盟管理员就具备管理权限）
  const isManager = coalitionDetail?.isManager
  // 判断是否为成员模式（从我的账号进入）
  const isMemberMode = source === 'my-account'

  useLoad(() => {
    console.log('CoalitionDetail page loaded.')
    if (!coalitionId) {
      Taro.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)
    }
  })

  useDidShow(() => {
    loadCoalitionDetail()
  })

  // 加载联盟详情
  const loadCoalitionDetail = useCallback(async () => {
    if (loading || !coalitionId) return

    setLoading(true)
    try {
      const detail = await sanBingApi.coalition.getDetail(coalitionId)
      setCoalitionDetail(detail)
    } catch (error) {
      console.error('加载联盟详情失败:', error)
      Taro.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }, [coalitionId])

  // 下拉刷新
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    await loadCoalitionDetail()
    setRefreshing(false)
  }, [loadCoalitionDetail])

  // 审核账号申请
  const handleApproveAccount = useCallback(async (accountId: string, approved: boolean) => {
    try {
      await sanBingApi.account.approve({
        accountId,
        approved
      })

      Taro.showToast({
        title: approved ? '审核通过' : '审核拒绝',
        icon: 'success'
      })

      // 刷新联盟详情
      await loadCoalitionDetail()
    } catch (error) {
      console.error('审核账号失败:', error)
      Taro.showToast({
        title: error.message || '审核失败',
        icon: 'none'
      })
    }
  }, [loadCoalitionDetail])

  // 创建分组
  const handleCreateGroup = useCallback(async (type: 'GUAN_DU1' | 'GUAN_DU2' | 'GONG_CHENG') => {
    if (!coalitionDetail || !isManager) return

    try {
      // 使用简单的确认对话框，分组名称使用默认格式
      const typeName = type === 'GUAN_DU1' ? '官渡一' : type === 'GUAN_DU2' ? '官渡二' : '攻城'
      const defaultName = `${typeName}分组${Date.now().toString().slice(-4)}`

      const confirmed = await new Promise<boolean>((resolve) => {
        Taro.showModal({
          title: `创建${typeName}分组`,
          content: `将创建名为"${defaultName}"的分组`,
          success: (res) => {
            resolve(res.confirm)
          }
        })
      })

      if (!confirmed) return

      await sanBingApi.group.create({
        coalitionId: coalitionDetail.coalitionId,
        name: defaultName,
        type
      })

      Taro.showToast({
        title: '分组创建成功',
        icon: 'success'
      })

      await loadCoalitionDetail()
    } catch (error) {
      console.error('创建分组失败:', error)
      Taro.showToast({
        title: error.message || '创建失败',
        icon: 'none'
      })
    }
  }, [coalitionDetail, isManager, loadCoalitionDetail])

  // 申请加入分组类型
  const handleApplyGroupType = useCallback(async (groupType: 'GUAN_DU1' | 'GUAN_DU2' | 'GONG_CHENG') => {
    if (!coalitionDetail) return

    try {
      // 如果从我的账号进入且有accountId，直接使用该账号申请
      if (isMemberMode && accountId) {
        await sanBingApi.account.applyGroup({
          accountId,
          groupType
        })

        Taro.showToast({
          title: '申请提交成功',
          icon: 'success'
        })

        await loadCoalitionDetail()
        return
      }

      // 否则需要获取用户在该区域的账号列表，让用户选择
      const areaAccounts = await sanBingApi.account.getMy()
      const currentAreaAccounts = areaAccounts.find(area => area.areaCode === coalitionDetail.areaCode)

      if (!currentAreaAccounts || currentAreaAccounts.accounts.length === 0) {
        Taro.showToast({
          title: `您在${coalitionDetail.areaCode}区还没有账号`,
          icon: 'none'
        })
        return
      }

      // 过滤出已加入该联盟的账号
      const coalitionAccounts = currentAreaAccounts.accounts.filter(account =>
        account.coalition?.coalitionId === coalitionDetail.coalitionId
      )

      if (coalitionAccounts.length === 0) {
        Taro.showToast({
          title: '您还没有账号加入该联盟',
          icon: 'none'
        })
        return
      }

      // 如果只有一个账号，直接使用
      if (coalitionAccounts.length === 1) {
        await sanBingApi.account.applyGroup({
          accountId: coalitionAccounts[0].accountId,
          groupType
        })

        Taro.showToast({
          title: '申请提交成功',
          icon: 'success'
        })

        await loadCoalitionDetail()
        return
      }

      // 多个账号时让用户选择
      const accountNames = coalitionAccounts.map(account =>
        `${account.name} (等级${account.level} 战力${formatCombatPower(account.combatPower)})`
      )

      Taro.showActionSheet({
        itemList: accountNames,
        success: async (res) => {
          const selectedAccount = coalitionAccounts[res.tapIndex]
          try {
            await sanBingApi.account.applyGroup({
              accountId: selectedAccount.accountId,
              groupType
            })

            Taro.showToast({
              title: '申请提交成功',
              icon: 'success'
            })

            await loadCoalitionDetail()
          } catch (error) {
            console.error('申请加入分组失败:', error)
            Taro.showToast({
              title: error.message || '申请失败',
              icon: 'none'
            })
          }
        }
      })

    } catch (error) {
      console.error('申请加入分组失败:', error)
      Taro.showToast({
        title: error.message || '申请失败',
        icon: 'none'
      })
    }
  }, [coalitionDetail, accountId, isMemberMode, loadCoalitionDetail])

  // 点击分组中的账号（管理员模式）
  const handleGroupAccountClick = useCallback((account: AccountInfo, groupType: string) => {
    if (!isManager) return

    const actions: string[] = []

    if (groupType === 'GUAN_DU1') {
      actions.push('申请到官渡二', '移动到官渡一其他分组')
    } else if (groupType === 'GUAN_DU2') {
      actions.push('申请到官渡一', '移动到官渡二其他分组')
    }

    if (actions.length === 0) return

    Taro.showActionSheet({
      itemList: actions,
      success: (res) => {
        // TODO: 实现具体的移动逻辑
        console.log('选择操作:', actions[res.tapIndex], account)
      }
    })
  }, [isManager])

  // 点击待审核账号处理
  const handlePendingAccountClick = useCallback((account: AccountInfo) => {
    if (!isManager) {
      return
    }

    Taro.showActionSheet({
      itemList: ['通过申请', '拒绝申请'],
      success: (res) => {
        if (res.tapIndex === 0) {
          handleApproveAccount(account.accountId, true)
        } else if (res.tapIndex === 1) {
          handleApproveAccount(account.accountId, false)
        }
      }
    })
  }, [isManager, handleApproveAccount])



  if (!coalitionDetail) {
    return (
      <View className='coalition-detail loading'>
        <Text>加载中...</Text>
      </View>
    )
  }

  return (
    <View className='coalition-detail'>
      <ScrollView
        className='scroll-container'
        scrollY
        refresherEnabled
        refresherTriggered={refreshing}
        onRefresherRefresh={handleRefresh}
      >
        {/* 联盟基本信息 */}
        <View className='coalition-header'>
          <View className='coalition-info'>
            <Text className='coalition-name'>{coalitionDetail.name}</Text>
            <Text className='coalition-code'>代码: {coalitionDetail.code}</Text>
            <Text className='coalition-area'>区服: {coalitionDetail.areaCode}区</Text>
            {coalitionDetail.isManager && (
              <View className='manager-badge'>
                <Text>管理员</Text>
              </View>
            )}
          </View>

          {/* 管理员操作按钮 */}
          {isManager && (
            <View className='manager-actions'>
              <Button
                type='default'
                size='mini'
                onClick={() => {
                  Taro.showToast({
                    title: '编辑联盟功能开发中',
                    icon: 'none'
                  })
                }}
              >
                编辑联盟
              </Button>
            </View>
          )}
        </View>

        {/* 联盟战事 */}
        <View className='section'>
          <View className='section-header'>
            <Text className='section-title'>联盟战事</Text>
          </View>
          {Object.keys(coalitionDetail.groupsByType).length > 0 ? (
            <View className='groups-container'>
              {Object.entries(coalitionDetail.groupsByType).map(([type, groups]) => (
                <View key={type} className='group-type-section'>
                  <Text className='group-type-title'>{getGroupTypeName(type)}</Text>
                  <View className='section-actions'>
                    {isManager && <Button
                      type='primary'
                      size='mini'
                      onClick={() => handleCreateGroup(type as any)}
                    >
                      新建分组
                    </Button>}
                    <Button
                      type='primary'
                      size='mini'
                      onClick={() => handleApplyGroupType(type as any)}
                    >
                      申请{getGroupTypeName(type)}
                    </Button>
                  </View>

                  {groups.map((group: GroupInfo) => (
                    <View key={group.groupId} className='group-item'>
                      <View className='group-header'>
                        <Text className='group-name'>{group.name}</Text>
                        {group.task && (
                          <Text className='group-task'>任务: {group.task}</Text>
                        )}
                        {isManager && (
                          <View className='group-actions'>
                            <Button
                              type='default'
                              size='mini'
                              onClick={() => {
                                // TODO: 编辑分组
                                console.log('编辑分组:', group)
                              }}
                            >
                              编辑
                            </Button>
                          </View>
                        )}
                      </View>

                      {group.accounts && group.accounts.length > 0 && (
                        <View className='group-accounts'>
                          {group.accounts.map((account: AccountInfo) => (
                            <View
                              key={account.accountId}
                              className={`account-item ${isManager ? 'clickable' : ''}`}
                              onClick={() => isManager && handleGroupAccountClick(account, type)}
                            >
                              <View className='account-info'>
                                <Text className='account-name'>{account.name}</Text>
                                <Text className='account-details'>
                                  等级{account.level} | 战力{formatCombatPower(account.combatPower)}
                                </Text>
                              </View>
                              <View className={`account-status ${getAccountStatusClass(account.status)}`}>
                                <Text className='status-text'>
                                  {getAccountStatusName(account.status)}
                                </Text>
                              </View>
                            </View>
                          ))}
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              ))}
            </View>
          ) : (
            <View className='empty-state'>
              <Text className='empty-text'>暂无分组</Text>
              {isManager && (
                <Text className='empty-desc'>点击上方按钮创建分组</Text>
              )}
              <Text className='empty-desc'>点击上方按钮申请加入战事</Text>
            </View>
          )}
        </View>

        {/* 申请中的账号 */}
        {Object.keys(coalitionDetail.applyAccountsByType).length > 0 && (
          <View className='section'>
            <View className='section-header'>
              <Text className='section-title'>申请中的账号</Text>
            </View>

            <View className='apply-accounts-container'>
              {Object.entries(coalitionDetail.applyAccountsByType).map(([type, accounts]) => (
                <View key={type} className='apply-type-section'>
                  <Text className='apply-type-title'>申请加入: {getGroupTypeName(type)}</Text>

                  {accounts.map((account: AccountInfo) => (
                    <View key={account.accountId} className='account-item'>
                      <View className='account-info'>
                        <Text className='account-name'>{account.name}</Text>
                        <Text className='account-details'>
                          等级{account.level} | 战力{formatCombatPower(account.combatPower)}
                        </Text>
                      </View>
                      <View className='account-status status-pending'>
                        <Text className='status-text'>待审核</Text>
                      </View>
                    </View>
                  ))}
                </View>
              ))}
            </View>
          </View>
        )}

        {/* 待审核账号 */}
        {isManager && coalitionDetail.pendingAccounts && coalitionDetail.pendingAccounts.length > 0 && (
          <View className='section'>
            <View className='section-header'>
              <Text className='section-title'>待审核账号</Text>
              <Text className='section-count'>({coalitionDetail.pendingAccounts.length})</Text>
            </View>

            <View className='pending-accounts-container'>
              {coalitionDetail.pendingAccounts.map((account: AccountInfo) => (
                <View
                  key={account.accountId}
                  className='account-item pending-account-item'
                  onClick={() => handlePendingAccountClick(account)}
                >
                  <View className='account-info'>
                    <Text className='account-name'>{account.name}</Text>
                    <Text className='account-details'>
                      等级{account.level} | 战力{formatCombatPower(account.combatPower)}
                    </Text>
                    {account.addition && (
                      <Text className='account-addition'>加成: {account.addition}%</Text>
                    )}
                    {account.gatheringCapacity && (
                      <Text className='account-gathering'>采集: {formatCombatPower(account.gatheringCapacity)}</Text>
                    )}
                  </View>
                  <View className='account-actions'>
                    <Button
                      type='primary'
                      size='mini'
                      className='approve-btn'
                      onClick={(e) => {
                        e.stopPropagation()
                        handleApproveAccount(account.accountId, true)
                      }}
                    >
                      通过
                    </Button>
                    <Button
                      type='default'
                      size='mini'
                      className='reject-btn'
                      onClick={(e) => {
                        e.stopPropagation()
                        handleApproveAccount(account.accountId, false)
                      }}
                    >
                      拒绝
                    </Button>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  )
}
