import { View, <PERSON>, <PERSON><PERSON>, ScrollView } from '@tarojs/components'
import { useLoad, useDidShow } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import { useState, useCallback } from 'react'
import { sanBingApi } from '../../services/api'
import { userStore } from '../../store/user'
import { formatCombatPower } from '../../utils/common'
import type { CoalitionInfo, AccountInfo, AreaAccount } from '../../types'
import './index.scss'

export default function SanBing() {
  const [coalitions, setCoalitions] = useState<CoalitionInfo[]>([])
  const [areaAccounts, setAreaAccounts] = useState<AreaAccount[]>([])
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  useLoad(() => {
    console.log('SanBing page loaded.')
  })

  useDidShow(() => {
    loadData()
  })

  // 加载数据
  const loadData = useCallback(async () => {
    if (loading) return

    setLoading(true)
    try {
      // 检查登录状态
      const isLoggedIn = await userStore.checkLoginStatus()
      if (!isLoggedIn) {
        await userStore.requireLogin()
        return
      }

      // 并行加载联盟和账号数据
      const [coalitionData, accountData] = await Promise.all([
        sanBingApi.coalition.getMy(),
        sanBingApi.account.getMy()
      ])

      setCoalitions(coalitionData)
      setAreaAccounts(accountData)
    } catch (error) {
      console.error('加载数据失败:', error)
      Taro.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }, [])

  // 下拉刷新
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    await loadData()
    setRefreshing(false)
  }, [loadData])

  // 创建联盟
  const handleCreateCoalition = () => {
    Taro.navigateTo({
      url: '/pages/sanbing/create-coalition/index'
    })
  }

  // 创建账号
  const handleCreateAccount = () => {
    Taro.navigateTo({
      url: '/pages/sanbing/create-account/index'
    })
  }

  // 点击联盟
  const handleCoalitionClick = (coalition: CoalitionInfo) => {
    Taro.navigateTo({
      url: `/pages/sanbing/coalition-detail/index?coalitionId=${coalition.coalitionId}&source=my-coalition`
    })
  }

  // 点击账号
  const handleAccountClick = (account: AccountInfo) => {
    if (!account.coalition) {
      // 账号未加入联盟，显示加入联盟对话框
      Taro.navigateTo({
        url: `/pages/sanbing/join-coalition/index?accountId=${account.accountId}`
      })
    } else {
      // 账号已加入联盟，跳转到联盟详情（从我的账号进入）
      Taro.navigateTo({
        url: `/pages/sanbing/coalition-detail/index?coalitionId=${account.coalition.coalitionId}&source=my-account&accountId=${account.accountId}`
      })
    }
  }

  return (
    <View className='sanbing'>
      <ScrollView
        className='scroll-container'
        scrollY
        refresherEnabled
        refresherTriggered={refreshing}
        onRefresherRefresh={handleRefresh}
      >
        {/* 头部 */}
        <View className='header'>
          <Text className='title'>三国冰河天下</Text>
          <Text className='subtitle'>管理你的联盟和账号</Text>
        </View>

        {/* 快捷操作 */}
        <View className='quick-actions'>
          <Button
            type='primary'
            onClick={handleCreateCoalition}
          >
            创建联盟
          </Button>
          <Button
            type='default'
            onClick={handleCreateAccount}
          >
            创建账号
          </Button>
        </View>

        {/* 我的联盟 */}
        <View className='section'>
          <View className='section-header'>
            <Text className='section-title'>我的联盟</Text>
            <Text className='section-count'>({coalitions.length})</Text>
          </View>

          {coalitions.length > 0 ? (
            <View className='coalition-list'>
              {coalitions.map(coalition => (
                <View
                  key={coalition.coalitionId}
                  className='coalition-item'
                  onClick={() => handleCoalitionClick(coalition)}
                >
                  <View className='coalition-info'>
                    <Text className='coalition-name'>{coalition.name}</Text>
                    <Text className='coalition-code'>代码: {coalition.code}</Text>
                    <Text className='coalition-area'>区服: {coalition.areaCode}区</Text>
                    <Text className='coalition-members'>成员: {coalition.memberCount}人</Text>
                    {coalition.pendingMemberCount > 0 && (
                      <Text className='coalition-pending'>待审核: {coalition.pendingMemberCount}人</Text>
                    )}
                  </View>
                  <View className='coalition-badge'>
                    <Text className='manager-badge'>管理员</Text>
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View className='empty-state'>
              <Text className='empty-text'>暂无联盟</Text>
              <Text className='empty-desc'>创建你的第一个联盟吧</Text>
            </View>
          )}
        </View>

        {/* 我的账号 */}
        <View className='section'>
          <View className='section-header'>
            <Text className='section-title'>我的账号</Text>
            {areaAccounts.length > 0 && <Text className='section-count'>({areaAccounts.reduce((total, area) => total + area.accounts.length, 0)})</Text>}
          </View>

          {areaAccounts.length > 0 ? (
            <View className='account-areas'>
              {areaAccounts.map(areaAccount => (
                <View key={areaAccount.areaCode} className='area-section'>
                  <View className='area-header'>
                    <Text className='area-title'>{areaAccount.areaCode}区</Text>
                    <Text className='area-count'>({areaAccount.accounts.length}个账号)</Text>
                  </View>
                  <View className='account-list'>
                    {areaAccount.accounts.map(account => (
                      <View
                        key={account.accountId}
                        className='account-item'
                        onClick={() => handleAccountClick(account)}
                      >
                        <View className='account-info'>
                          <Text className='account-name'>{account.name}</Text>
                          <Text className='account-level'>等级: {account.level}</Text>
                          <Text className='account-power'>战力: {formatCombatPower(account.combatPower)}</Text>
                        </View>
                        <View className='account-status'>
                          {account.coalition ? (
                            <View className='joined-coalition'>
                              <Text className='coalition-name'>{account.coalition.name}</Text>
                              <Text className='status-text'>已加入</Text>
                            </View>
                          ) : (
                            <Text className='not-joined'>未加入联盟</Text>
                          )}
                        </View>
                      </View>
                    ))}
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View className='empty-state'>
              <Text className='empty-text'>暂无账号</Text>
              <Text className='empty-desc'>创建你的第一个游戏账号吧</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  )
}
