import { View, Text, Button } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import { useState } from 'react'
import { userStore } from '../../store/user'
import './index.scss'

export default function Login() {
  const [loading, setLoading] = useState(false)

  useLoad(() => {
    console.log('Login page loaded.')
  })

  // 处理微信登录
  const handleWechatLogin = async () => {
    if (loading) return

    setLoading(true)
    try {
      const success = await userStore.wechatLogin()
      if (success) {
        Taro.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        // 登录成功后返回上一页或跳转到首页
        setTimeout(() => {
          const pages = Taro.getCurrentPages()
          if (pages.length > 1) {
            Taro.navigateBack()
          } else {
            Taro.switchTab({
              url: '/pages/index/index'
            })
          }
        }, 1500)
      }
    } catch (error) {
      console.error('登录失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <View className='login'>
      <View className='login-container'>
        <View className='logo-section'>
          <View className='logo'>
            <Text className='logo-text'>战术室</Text>
          </View>
          <Text className='welcome-text'>欢迎来到游戏战术室</Text>
          <Text className='desc-text'>管理你的游戏联盟和账号</Text>
        </View>

        <View className='login-section'>
          <Button 
            className='wechat-login-btn'
            onClick={handleWechatLogin}
            loading={loading}
            disabled={loading}
          >
            {loading ? '登录中...' : '微信登录'}
          </Button>
          
          <View className='login-tips'>
            <Text className='tips-text'>点击登录即表示同意用户协议和隐私政策</Text>
          </View>
        </View>
      </View>
    </View>
  )
}
