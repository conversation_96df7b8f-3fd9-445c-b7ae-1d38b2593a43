.login {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 30px;

  .login-container {
    width: 100%;
    max-width: 600px;
    text-align: center;
  }

  .logo-section {
    margin-bottom: 100px;

    .logo {
      width: 120px;
      height: 120px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      margin: 0 auto 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.3);

      .logo-text {
        font-size: 36px;
        font-weight: bold;
        color: #ffffff;
      }
    }

    .welcome-text {
      display: block;
      font-size: 42px;
      font-weight: bold;
      color: #ffffff;
      margin-bottom: 20px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .desc-text {
      display: block;
      font-size: 28px;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .login-section {
    .wechat-login-btn {
      width: 100%;
      height: 88px;
      background: #07c160;
      color: #ffffff;
      border: none;
      border-radius: 44px;
      font-size: 32px;
      font-weight: 500;
      margin-bottom: 40px;
      box-shadow: 0 8px 32px rgba(7, 193, 96, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4px 16px rgba(7, 193, 96, 0.4);
      }

      &.button-loading {
        background: rgba(7, 193, 96, 0.7);
      }
    }

    .login-tips {
      .tips-text {
        font-size: 22px;
        color: rgba(255, 255, 255, 0.6);
        line-height: 1.4;
      }
    }
  }
}
