import request from '../utils/request'
import type {
  UserInfo,
  LoginResult,
  CoalitionInfo,
  AreaAccount,
  CoalitionDetailInfo,
  CoalitionCreateDTO,
  CoalitionUpdateDTO,
  AccountCreateDTO,
  AccountJoinCoalitionDTO,
  AccountApproveDTO,
  GroupCreateDTO,
  GroupUpdateDTO,
  AccountApplyGroupDTO,
  AccountAddToGroupDTO,
  OperationResult
} from '../types'

// 用户相关API
export const userApi = {
  // 微信登录
  wechatLogin: (data: {
    code: string
    nickName?: string
    avatarUrl?: string
    gender?: number
    country?: string
    province?: string
    city?: string
    language?: string
  }): Promise<LoginResult> => {
    return request({
      url: '/user/wechat-login',
      method: 'POST',
      data
    })
  },

  // 获取用户信息
  getUserInfo: (): Promise<UserInfo> => {
    return request({
      url: '/user/info',
      method: 'GET'
    })
  },

  // 用户登出
  logout: (): Promise<string> => {
    return request({
      url: '/user/logout',
      method: 'POST'
    })
  }
}

// 三冰游戏相关API
export const sanBingApi = {
  // 联盟相关
  coalition: {
    // 创建联盟
    create: (data: CoalitionCreateDTO): Promise<{ coalitionId: string; code: string }> => {
      return request({
        url: '/sanBing/coalition',
        method: 'POST',
        data
      })
    },

    // 查询我的联盟
    getMy: (): Promise<CoalitionInfo[]> => {
      return request({
        url: '/sanBing/coalition/my',
        method: 'GET'
      })
    },

    // 获取联盟详情
    getDetail: (coalitionId: string): Promise<CoalitionDetailInfo> => {
      return request({
        url: `/sanBing/coalition/${coalitionId}`,
        method: 'GET'
      })
    },

    // 更新联盟信息
    update: (data: CoalitionUpdateDTO): Promise<void> => {
      return request({
        url: '/sanBing/coalition',
        method: 'PUT',
        data
      })
    }
  },

  // 账号相关
  account: {
    // 创建账号
    create: (data: AccountCreateDTO): Promise<{ accountId: string; name: string }> => {
      return request({
        url: '/sanBing/account',
        method: 'POST',
        data
      })
    },

    // 查询我的账号
    getMy: (): Promise<AreaAccount[]> => {
      return request({
        url: '/sanBing/account/my',
        method: 'GET'
      })
    },

    // 申请加入联盟
    joinCoalition: (data: AccountJoinCoalitionDTO): Promise<OperationResult> => {
      return request({
        url: '/sanBing/account/join',
        method: 'POST',
        data
      })
    },

    // 审核账号申请
    approve: (data: AccountApproveDTO): Promise<OperationResult> => {
      return request({
        url: '/sanBing/account/approve',
        method: 'POST',
        data
      })
    },

    // 更新账号信息
    update: (data: {
      accountId: string
      name: string
      level: number
      combatPower: number
      addition?: number
      gatheringCapacity?: number
    }): Promise<OperationResult> => {
      return request({
        url: '/sanBing/account',
        method: 'PUT',
        data
      })
    },

    // 申请加入分组类型
    applyGroup: (data: AccountApplyGroupDTO): Promise<OperationResult> => {
      return request({
        url: '/sanBing/account/apply-group',
        method: 'POST',
        data
      })
    }
  },

  // 分组相关
  group: {
    // 创建分组
    create: (data: GroupCreateDTO): Promise<OperationResult> => {
      return request({
        url: '/sanBing/group',
        method: 'POST',
        data
      })
    },

    // 更新分组
    update: (data: GroupUpdateDTO): Promise<OperationResult> => {
      return request({
        url: '/sanBing/group',
        method: 'PUT',
        data
      })
    },

    // 删除分组
    delete: (groupId: string): Promise<OperationResult> => {
      return request({
        url: `/sanBing/group/${groupId}`,
        method: 'DELETE'
      })
    },

    // 添加账号到分组
    addAccount: (data: AccountAddToGroupDTO): Promise<OperationResult> => {
      return request({
        url: '/sanBing/group/add-account',
        method: 'POST',
        data
      })
    },

    // 清空分组类型账号
    clearType: (coalitionId: string, groupType: string): Promise<OperationResult> => {
      return request({
        url: `/sanBing/group/clear/${coalitionId}/${groupType}`,
        method: 'DELETE'
      })
    }
  }
}
