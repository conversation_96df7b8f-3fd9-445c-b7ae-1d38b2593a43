// 用户相关类型定义
export interface UserInfo {
  userId: string  // 使用字符串避免大数字精度丢失
  nickName: string
  avatarUrl?: string
}

export interface LoginResult {
  userId: string  // 使用字符串避免大数字精度丢失
  nickName: string
  avatarUrl?: string
  isNewUser: boolean
  token: string
}

// 三冰游戏相关类型定义
export interface CoalitionInfo {
  coalitionId: string  // 使用字符串避免大数字精度丢失
  name: string
  code: string
  areaCode: number
  memberCount: number
  pendingMemberCount: number
}

export interface CoalitionSimpleInfo {
  coalitionId: string  // 使用字符串避免大数字精度丢失
  name: string
  code: string
}

export interface AccountInfo {
  accountId: string  // 使用字符串避免大数字精度丢失
  name: string
  level: number
  combatPower: number
  addition: number
  gatheringCapacity: number
  status: 'TO_BE_APPROVED' | 'APPROVED' | 'REJECT'
  coalition?: CoalitionSimpleInfo
}

export interface AreaAccount {
  areaCode: number
  accounts: AccountInfo[]
}

export interface CoalitionDetailInfo {
  coalitionId: string  // 使用字符串避免大数字精度丢失
  name: string
  code: string
  areaCode: number
  managerId: string  // 使用字符串避免大数字精度丢失
  isManager: boolean
  groupsByType: Record<string, GroupInfo[]>
  applyAccountsByType: Record<string, AccountInfo[]>
  pendingAccounts?: AccountInfo[]  // 待审核的账号列表（仅管理员可见）
}

export interface GroupInfo {
  groupId: string  // 使用字符串避免大数字精度丢失
  name: string
  type: string
  task?: string
  accounts: AccountInfo[]
}

// 请求DTO类型
export interface CoalitionCreateDTO {
  areaCode: number
  name: string
}

export interface AccountCreateDTO {
  areaCode: number
  name: string
  combatPower: number
  addition?: number
  gatheringCapacity?: number
}

export interface AccountJoinCoalitionDTO {
  accountId: string  // 使用字符串避免大数字精度丢失
  coalitionCode: string
  level: number
}

export interface AccountApproveDTO {
  accountId: string  // 使用字符串避免大数字精度丢失
  approved: boolean
}

export interface GroupCreateDTO {
  coalitionId: string  // 使用字符串避免大数字精度丢失
  name: string
  type: 'GUAN_DU1' | 'GUAN_DU2' | 'GONG_CHENG'
  task?: string
}

export interface GroupUpdateDTO {
  groupId: string  // 使用字符串避免大数字精度丢失
  name: string
  task?: string
}

export interface AccountApplyGroupDTO {
  accountId: string  // 使用字符串避免大数字精度丢失
  groupType: 'GUAN_DU1' | 'GUAN_DU2' | 'GONG_CHENG'
}

export interface AccountAddToGroupDTO {
  accountId: string  // 使用字符串避免大数字精度丢失
  groupId: string  // 使用字符串避免大数字精度丢失
}

export interface CoalitionUpdateDTO {
  coalitionId: string  // 使用字符串避免大数字精度丢失
  name: string
  code: string
}

export interface OperationResult {
  success: boolean
  message: string
}

// 游戏信息类型
export interface GameInfo {
  id: string
  name: string
  description: string
  image: string
  route: string
}
